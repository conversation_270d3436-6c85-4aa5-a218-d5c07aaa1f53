defmodule Repobot.SourceFiles.FormComponent.Save do
  use Repobot.Operation, type: :form

  require Repobot.SourceFile
  alias Repobot.{SourceFiles, Tags, SourceFile}

  schema(SourceFile,
    accept: [:name, :target_path, :user_id, :organization_id, :is_template],
    default_presence: :optional
  )

  @impl true
  def prepare(%{params: %{is_template: true, name: name} = params} = context) do
    if String.ends_with?(name, ".liquid") do
      {:ok, context}
    else
      {:ok, %{context | params: %{params | name: name <> ".liquid"}}}
    end
  end

  @impl true
  def get_struct(%{source_file: source_file}), do: source_file

  @impl true
  def validate_changeset(%{changeset: changeset}) do
    changeset
    |> Ecto.Changeset.validate_required([:name, :target_path])
  end

  @impl true
  def execute(%{action: :new, changeset: changeset, content: content} = context) do
    updated_changeset = Ecto.Changeset.put_assoc(changeset, :tags, tags(context))

    SourceFiles.create_source_file_from_changeset(updated_changeset, content)
  end

  def execute(
        %{action: :edit, source_file: source_file, changeset: changeset, content: content} =
          context
      ) do
    updated_changeset = Ecto.Changeset.put_assoc(changeset, :tags, tags(context))

    SourceFiles.update_source_file_from_changeset(source_file, updated_changeset, content)
  end

  defp tags(%{current_user: user, current_organization: organization, tag_names: tag_names}) do
    if length(tag_names) > 0 do
      Tags.get_or_create_tags(tag_names, organization, user)
    else
      []
    end
  end
end
